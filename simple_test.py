#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط لنظام Gemini 2.5 Pro
"""

import asyncio
import sys
import os

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def simple_test():
    """اختبار مبسط"""
    print("🚀 اختبار مبسط لنظام Gemini 2.5 Pro...")
    
    try:
        # اختبار الاستيراد
        from modules.gemini_enhanced_system import gemini_enhanced_system
        print("✅ تم استيراد النظام بنجاح")
        
        # فحص حالة النظام
        if gemini_enhanced_system.enabled:
            print("✅ النظام مفعل")
        else:
            print("❌ النظام غير مفعل")
            return
        
        # عرض الإحصائيات
        stats = await gemini_enhanced_system.get_stats()
        print(f"📊 إحصائيات النظام:")
        print(f"   - النظام مفعل: {stats['enabled']}")
        print(f"   - إجمالي التحليلات: {stats['total_analyses']}")
        print(f"   - معدل النجاح: {stats['success_rate']:.1f}%")
        
        # اختبار التخزين المؤقت
        await gemini_enhanced_system.clear_cache()
        print("✅ تم مسح التخزين المؤقت")
        
        print("\n🎉 الاختبار المبسط نجح!")
        print("✨ النظام جاهز للاستخدام")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

async def test_integration():
    """اختبار التكامل"""
    print("\n🔗 اختبار نظام التكامل...")
    
    try:
        from modules.gemini_agent_integration import gemini_agent_integration
        print("✅ تم استيراد نظام التكامل")
        
        # عرض إحصائيات التحسين
        stats = await gemini_agent_integration.get_enhancement_stats()
        print(f"📈 إحصائيات التحسين:")
        print(f"   - النظام مفعل: {stats['enabled']}")
        print(f"   - إجمالي التحليلات المحسنة: {stats['total_enhanced_analyses']}")
        
        print("✅ نظام التكامل يعمل بشكل صحيح")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {e}")

async def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🧪 اختبار مبسط لنظام Gemini 2.5 Pro")
    print("=" * 50)
    
    await simple_test()
    await test_integration()
    
    print("\n" + "=" * 50)
    print("✅ تم إنجاز جميع الاختبارات المبسطة")
    print("🎯 النظام جاهز لاستبدال RAG")
    print("=" * 50)

if __name__ == "__main__":
    asyncio.run(main())
