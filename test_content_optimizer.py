#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام التحسين التلقائي للمحتوى
"""

import asyncio
import sys
import os
import sqlite3
from datetime import datetime, timedelta

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.content_optimizer import content_optimizer
from modules.logger import logger

def create_poor_performing_articles():
    """إنشاء مقالات ضعيفة الأداء للاختبار"""
    try:
        with sqlite3.connect("data/articles.db") as conn:
            cursor = conn.cursor()
            
            # إنشاء مقالات ضعيفة الأداء
            poor_articles = [
                {
                    'title': 'لعبة جديدة',  # عنوان ضعيف
                    'content': 'هذه لعبة جديدة. إنها جيدة.',  # محتوى قصير وضعيف
                    'keywords': 'لعبة',  # كلمات مفتاحية قليلة
                    'category': 'عام',
                    'published_at': (datetime.now() - timedelta(days=3)).isoformat(),
                    'view_count': 15,  # مشاهدات قليلة
                    'engagement_score': 25.0  # تفاعل ضعيف
                },
                {
                    'title': 'مراجعة',  # عنوان غير واضح
                    'content': 'مراجعة سريعة للعبة. اللعبة عادية.',  # محتوى ضعيف
                    'keywords': 'مراجعة, لعبة',
                    'category': 'مراجعات',
                    'published_at': (datetime.now() - timedelta(days=5)).isoformat(),
                    'view_count': 8,
                    'engagement_score': 20.5
                },
                {
                    'title': 'خبر عن الألعاب',  # عنوان عام
                    'content': 'خبر جديد في عالم الألعاب. شيء مثير.',  # محتوى غير مفيد
                    'keywords': 'خبر, ألعاب',
                    'category': 'أخبار',
                    'published_at': (datetime.now() - timedelta(days=1)).isoformat(),
                    'view_count': 30,
                    'engagement_score': 35.2
                },
                {
                    'title': 'دليل',  # عنوان غير مكتمل
                    'content': 'دليل بسيط. خطوات قليلة.',  # محتوى قصير جداً
                    'keywords': 'دليل',
                    'category': 'أدلة',
                    'published_at': (datetime.now() - timedelta(days=7)).isoformat(),
                    'view_count': 5,
                    'engagement_score': 15.8
                }
            ]
            
            for article in poor_articles:
                cursor.execute('''
                    INSERT OR IGNORE INTO published_articles 
                    (title, content, keywords, category, published_at, view_count, engagement_score)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    article['title'],
                    article['content'],
                    article['keywords'],
                    article['category'],
                    article['published_at'],
                    article['view_count'],
                    article['engagement_score']
                ))
            
            conn.commit()
            print("✅ تم إنشاء المقالات ضعيفة الأداء للاختبار")
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء المقالات ضعيفة الأداء: {e}")

async def test_identify_poor_articles():
    """اختبار تحديد المقالات ضعيفة الأداء"""
    print("🔍 اختبار تحديد المقالات ضعيفة الأداء...")
    
    try:
        poor_articles = await content_optimizer.identify_poor_performing_articles()
        
        if poor_articles:
            print(f"✅ تم تحديد {len(poor_articles)} مقال ضعيف الأداء:")
            
            for i, article in enumerate(poor_articles[:5], 1):
                print(f"   {i}. {article['title'][:40]}...")
                print(f"      الأولوية: {article['priority']}")
                print(f"      النقاط الإجمالية: {article['overall_score']:.1f}")
                print(f"      المشاكل: {', '.join(article['issues'][:2])}")
                print()
            
            return True
        else:
            print("⚠️ لم يتم العثور على مقالات ضعيفة الأداء")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في تحديد المقالات ضعيفة الأداء: {e}")
        return False

async def test_title_optimization():
    """اختبار تحسين العناوين"""
    print("\n📝 اختبار تحسين العناوين...")
    
    try:
        # مقال تجريبي بعنوان ضعيف
        test_article = {
            'title': 'لعبة جديدة',
            'content': 'هذه مراجعة للعبة جديدة رائعة في عالم الألعاب. اللعبة تحتوي على مميزات كثيرة ورسومات جميلة.',
            'keywords': 'لعبة, مراجعة'
        }
        
        # تحسين العنوان
        optimized_title = await content_optimizer._optimize_title(test_article)
        
        if optimized_title:
            print(f"✅ تم تحسين العنوان:")
            print(f"   الأصلي: {test_article['title']}")
            print(f"   المحسن: {optimized_title}")
            return True
        else:
            print("❌ فشل في تحسين العنوان")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تحسين العناوين: {e}")
        return False

async def test_content_optimization():
    """اختبار تحسين المحتوى"""
    print("\n📄 اختبار تحسين المحتوى...")
    
    try:
        # مقال تجريبي بمحتوى ضعيف
        test_article = {
            'title': 'مراجعة لعبة Cyberpunk 2077',
            'content': 'لعبة جيدة. رسومات حلوة. قصة مثيرة.',
            'keywords': 'cyberpunk, مراجعة, لعبة'
        }
        
        # تحسين المحتوى
        optimized_content = await content_optimizer._optimize_content(test_article)
        
        if optimized_content:
            print(f"✅ تم تحسين المحتوى:")
            print(f"   الأصلي ({len(test_article['content'])} حرف): {test_article['content']}")
            print(f"   المحسن ({len(optimized_content)} حرف): {optimized_content[:100]}...")
            return True
        else:
            print("❌ فشل في تحسين المحتوى")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تحسين المحتوى: {e}")
        return False

async def test_keywords_optimization():
    """اختبار تحسين الكلمات المفتاحية"""
    print("\n🔑 اختبار تحسين الكلمات المفتاحية...")
    
    try:
        # مقال تجريبي بكلمات مفتاحية ضعيفة
        test_article = {
            'title': 'مراجعة شاملة للعبة The Last of Us Part II',
            'content': 'مراجعة مفصلة للعبة The Last of Us Part II من ناتي دوغ. اللعبة تحتوي على قصة مؤثرة ورسومات رائعة.',
            'keywords': 'لعبة'
        }
        
        # تحسين الكلمات المفتاحية
        optimized_keywords = await content_optimizer._optimize_keywords(test_article)
        
        if optimized_keywords:
            print(f"✅ تم تحسين الكلمات المفتاحية:")
            print(f"   الأصلية: {test_article['keywords']}")
            print(f"   المحسنة: {optimized_keywords}")
            return True
        else:
            print("❌ فشل في تحسين الكلمات المفتاحية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تحسين الكلمات المفتاحية: {e}")
        return False

async def test_category_optimization():
    """اختبار تحسين التصنيف"""
    print("\n📂 اختبار تحسين التصنيف...")
    
    try:
        # مقالات تجريبية بتصنيفات مختلفة
        test_articles = [
            {
                'title': 'مراجعة شاملة للعبة FIFA 2025',
                'content': 'مراجعة مفصلة للعبة FIFA الجديدة مع تقييم شامل للمميزات',
                'category': 'عام'
            },
            {
                'title': 'خبر عاجل: إعلان Grand Theft Auto VI',
                'content': 'خبر عاجل عن إعلان لعبة GTA VI من شركة Rockstar Games',
                'category': 'عام'
            },
            {
                'title': 'دليل المبتدئين للعبة Minecraft',
                'content': 'دليل شامل للمبتدئين في لعبة Minecraft مع نصائح وحيل',
                'category': 'عام'
            }
        ]
        
        success_count = 0
        
        for article in test_articles:
            optimized_category = await content_optimizer._optimize_category_and_tags(article)
            
            if optimized_category:
                print(f"✅ {article['title'][:30]}...")
                print(f"   التصنيف الأصلي: {article['category']}")
                print(f"   التصنيف المحسن: {optimized_category}")
                success_count += 1
            else:
                print(f"❌ فشل في تحسين تصنيف: {article['title'][:30]}...")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تحسين التصنيف: {e}")
        return False

async def test_full_optimization():
    """اختبار التحسين الكامل"""
    print("\n🚀 اختبار التحسين التلقائي الكامل...")
    
    try:
        # تشغيل التحسين التلقائي
        results = await content_optimizer.run_automatic_optimization()
        
        if results.get('optimized_count', 0) > 0:
            print(f"✅ تم تحسين {results['optimized_count']} مقال")
            print(f"📊 إجمالي المقالات المحددة: {results.get('total_identified', 0)}")
            
            # عرض ملخص التحسينات
            summary = results.get('optimization_summary', [])
            if summary:
                print("\n📋 ملخص التحسينات:")
                for i, opt in enumerate(summary, 1):
                    print(f"   {i}. {opt['title'][:40]}...")
                    print(f"      التحسينات: {', '.join(opt['improvements'])}")
                    print(f"      الأولوية: {opt['priority']}")
            
            return True
        else:
            print("ℹ️ لا توجد مقالات تحتاج تحسين حالياً")
            return True  # هذا ليس خطأ
        
    except Exception as e:
        print(f"❌ خطأ في التحسين التلقائي الكامل: {e}")
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبارات نظام التحسين التلقائي للمحتوى")
    print("=" * 60)
    
    # إنشاء مقالات ضعيفة الأداء للاختبار
    create_poor_performing_articles()
    
    # اختبار تحديد المقالات ضعيفة الأداء
    identify_success = await test_identify_poor_articles()
    
    # اختبار تحسين العناوين
    title_success = await test_title_optimization()
    
    # اختبار تحسين المحتوى
    content_success = await test_content_optimization()
    
    # اختبار تحسين الكلمات المفتاحية
    keywords_success = await test_keywords_optimization()
    
    # اختبار تحسين التصنيف
    category_success = await test_category_optimization()
    
    # اختبار التحسين الكامل
    full_optimization_success = await test_full_optimization()
    
    print("\n" + "=" * 60)
    print("📋 ملخص نتائج الاختبارات:")
    print(f"🔍 تحديد المقالات الضعيفة: {'✅ نجح' if identify_success else '❌ فشل'}")
    print(f"📝 تحسين العناوين: {'✅ نجح' if title_success else '❌ فشل'}")
    print(f"📄 تحسين المحتوى: {'✅ نجح' if content_success else '❌ فشل'}")
    print(f"🔑 تحسين الكلمات المفتاحية: {'✅ نجح' if keywords_success else '❌ فشل'}")
    print(f"📂 تحسين التصنيف: {'✅ نجح' if category_success else '❌ فشل'}")
    print(f"🚀 التحسين التلقائي الكامل: {'✅ نجح' if full_optimization_success else '❌ فشل'}")
    
    all_tests = [identify_success, title_success, content_success, keywords_success, category_success, full_optimization_success]
    
    if all(all_tests):
        print("\n🎉 جميع الاختبارات نجحت! نظام التحسين التلقائي جاهز للاستخدام.")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء.")

if __name__ == "__main__":
    asyncio.run(main())
