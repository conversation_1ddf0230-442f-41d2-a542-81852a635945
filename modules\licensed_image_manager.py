#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الصور المرخصة - الحصول على صور الألعاب من مصادر قانونية
"""

import asyncio
import aiohttp
import json
import os
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import hashlib

from .logger import logger
from config.settings import BotConfig

@dataclass
class LicensedImage:
    """معلومات الصورة المرخصة"""
    url: str
    source: str
    license_type: str
    attribution: str
    game_name: str
    image_type: str  # screenshot, cover, artwork, etc.
    width: int = 0
    height: int = 0
    safe_for_adsense: bool = True
    copyright_free: bool = True

class IGDBImageProvider:
    """موفر الصور من IGDB API"""
    
    def __init__(self):
        # IGDB يتطلب Twitch Client ID و Secret
        self.client_id = getattr(BotConfig, 'TWITCH_CLIENT_ID', '')
        self.client_secret = getattr(BotConfig, 'TWITCH_CLIENT_SECRET', '')
        self.access_token = None
        self.token_expires_at = None
        
        self.base_url = "https://api.igdb.com/v4"
        self.auth_url = "https://id.twitch.tv/oauth2/token"
        
        # Cache للصور
        self.image_cache = {}
        self.cache_duration = timedelta(hours=24)
        
        logger.info("🎮 تم تهيئة موفر صور IGDB")
    
    async def _get_access_token(self) -> bool:
        """الحصول على رمز الوصول من Twitch"""
        try:
            if not self.client_id or not self.client_secret:
                logger.warning("⚠️ معرف العميل أو السر غير متوفر لـ IGDB")
                return False
            
            # فحص إذا كان الرمز ما زال صالحاً
            if (self.access_token and self.token_expires_at and 
                datetime.now() < self.token_expires_at):
                return True
            
            # طلب رمز جديد
            data = {
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'grant_type': 'client_credentials'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(self.auth_url, data=data) as response:
                    if response.status == 200:
                        token_data = await response.json()
                        self.access_token = token_data['access_token']
                        expires_in = token_data.get('expires_in', 3600)
                        self.token_expires_at = datetime.now() + timedelta(seconds=expires_in - 300)
                        
                        logger.info("✅ تم الحصول على رمز الوصول لـ IGDB")
                        return True
                    else:
                        logger.error(f"❌ فشل في الحصول على رمز الوصول: {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على رمز الوصول: {e}")
            return False
    
    async def search_game_images(self, game_name: str, max_images: int = 3) -> List[LicensedImage]:
        """البحث عن صور اللعبة في IGDB"""
        try:
            if not await self._get_access_token():
                return []
            
            # تنظيف اسم اللعبة للبحث
            clean_name = self._clean_game_name(game_name)
            cache_key = f"igdb_{hashlib.md5(clean_name.encode()).hexdigest()}"
            
            # فحص الكاش
            if cache_key in self.image_cache:
                cached_data = self.image_cache[cache_key]
                if datetime.now() - cached_data['timestamp'] < self.cache_duration:
                    logger.info(f"📦 استخدام صور محفوظة لـ {game_name}")
                    return cached_data['images']
            
            # البحث عن اللعبة أولاً
            game_data = await self._search_game(clean_name)
            if not game_data:
                return []
            
            game_id = game_data['id']
            images = []
            
            # الحصول على أنواع مختلفة من الصور
            image_types = [
                ('screenshots', 'screenshot'),
                ('cover', 'cover'),
                ('artworks', 'artwork')
            ]
            
            for endpoint, image_type in image_types:
                if len(images) >= max_images:
                    break
                    
                type_images = await self._get_game_images(game_id, endpoint, image_type)
                images.extend(type_images[:max_images - len(images)])
            
            # حفظ في الكاش
            self.image_cache[cache_key] = {
                'images': images,
                'timestamp': datetime.now()
            }
            
            logger.info(f"🎮 تم العثور على {len(images)} صورة لـ {game_name} من IGDB")
            return images
            
        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن صور {game_name} في IGDB: {e}")
            return []
    
    async def _search_game(self, game_name: str) -> Optional[Dict]:
        """البحث عن اللعبة في IGDB"""
        try:
            headers = {
                'Client-ID': self.client_id,
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }
            
            # استعلام البحث
            query = f'search "{game_name}"; fields id,name,cover,screenshots,artworks; limit 1;'
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/games",
                    headers=headers,
                    data=query
                ) as response:
                    if response.status == 200:
                        games = await response.json()
                        if games:
                            return games[0]
                    else:
                        logger.warning(f"⚠️ IGDB API استجاب بحالة {response.status}")
                        
            return None
            
        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن اللعبة: {e}")
            return None
    
    async def _get_game_images(self, game_id: int, endpoint: str, image_type: str) -> List[LicensedImage]:
        """الحصول على صور اللعبة من نوع معين"""
        try:
            headers = {
                'Client-ID': self.client_id,
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }
            
            # استعلام الصور
            if endpoint == 'cover':
                query = f'fields url,width,height; where game = {game_id};'
                api_endpoint = 'covers'
            else:
                query = f'fields url,width,height; where game = {game_id}; limit 5;'
                api_endpoint = endpoint
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/{api_endpoint}",
                    headers=headers,
                    data=query
                ) as response:
                    if response.status == 200:
                        images_data = await response.json()
                        return self._process_igdb_images(images_data, image_type)
                    else:
                        logger.warning(f"⚠️ فشل في الحصول على {endpoint}: {response.status}")
                        
            return []
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على {endpoint}: {e}")
            return []
    
    def _process_igdb_images(self, images_data: List[Dict], image_type: str) -> List[LicensedImage]:
        """معالجة بيانات الصور من IGDB"""
        licensed_images = []
        
        for img_data in images_data:
            try:
                # تحويل URL إلى دقة عالية
                url = img_data.get('url', '')
                if url:
                    # إزالة // من البداية وإضافة https
                    if url.startswith('//'):
                        url = 'https:' + url
                    
                    # تحويل إلى دقة عالية
                    url = url.replace('t_thumb', 't_1080p')
                    
                    licensed_image = LicensedImage(
                        url=url,
                        source='IGDB',
                        license_type='IGDB Terms of Use',
                        attribution='Image provided by IGDB.com',
                        game_name='',  # سيتم تعيينه لاحقاً
                        image_type=image_type,
                        width=img_data.get('width', 0),
                        height=img_data.get('height', 0),
                        safe_for_adsense=True,
                        copyright_free=True
                    )
                    
                    licensed_images.append(licensed_image)
                    
            except Exception as e:
                logger.warning(f"⚠️ خطأ في معالجة صورة IGDB: {e}")
                continue
        
        return licensed_images
    
    def _clean_game_name(self, game_name: str) -> str:
        """تنظيف اسم اللعبة للبحث"""
        # إزالة الكلمات الشائعة
        clean_name = re.sub(r'\b(game|لعبة|review|مراجعة|news|أخبار)\b', '', game_name, flags=re.IGNORECASE)
        
        # إزالة الأرقام والرموز الزائدة
        clean_name = re.sub(r'[^\w\s]', ' ', clean_name)
        clean_name = re.sub(r'\s+', ' ', clean_name).strip()
        
        return clean_name

class RAWGImageProvider:
    """موفر الصور من RAWG API"""
    
    def __init__(self):
        self.api_key = getattr(BotConfig, 'RAWG_API_KEY', '')
        self.base_url = "https://api.rawg.io/api"
        
        # Cache للصور
        self.image_cache = {}
        self.cache_duration = timedelta(hours=24)
        
        logger.info("🎯 تم تهيئة موفر صور RAWG")
    
    async def search_game_images(self, game_name: str, max_images: int = 3) -> List[LicensedImage]:
        """البحث عن صور اللعبة في RAWG"""
        try:
            clean_name = self._clean_game_name(game_name)
            cache_key = f"rawg_{hashlib.md5(clean_name.encode()).hexdigest()}"
            
            # فحص الكاش
            if cache_key in self.image_cache:
                cached_data = self.image_cache[cache_key]
                if datetime.now() - cached_data['timestamp'] < self.cache_duration:
                    logger.info(f"📦 استخدام صور RAWG محفوظة لـ {game_name}")
                    return cached_data['images']
            
            # البحث عن اللعبة
            game_data = await self._search_game(clean_name)
            if not game_data:
                return []
            
            # الحصول على الصور
            images = await self._get_game_screenshots(game_data['id'], max_images)
            
            # إضافة صورة الغلاف إذا كانت متوفرة
            if game_data.get('background_image'):
                cover_image = LicensedImage(
                    url=game_data['background_image'],
                    source='RAWG',
                    license_type='RAWG Terms of Use',
                    attribution='Image provided by RAWG.io',
                    game_name=game_data.get('name', ''),
                    image_type='cover',
                    safe_for_adsense=True,
                    copyright_free=True
                )
                images.insert(0, cover_image)
            
            # حفظ في الكاش
            self.image_cache[cache_key] = {
                'images': images[:max_images],
                'timestamp': datetime.now()
            }
            
            logger.info(f"🎯 تم العثور على {len(images[:max_images])} صورة لـ {game_name} من RAWG")
            return images[:max_images]
            
        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن صور {game_name} في RAWG: {e}")
            return []
    
    async def _search_game(self, game_name: str) -> Optional[Dict]:
        """البحث عن اللعبة في RAWG"""
        try:
            params = {
                'search': game_name,
                'page_size': 1
            }
            
            if self.api_key:
                params['key'] = self.api_key
            
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/games", params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('results'):
                            return data['results'][0]
                    else:
                        logger.warning(f"⚠️ RAWG API استجاب بحالة {response.status}")
                        
            return None
            
        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن اللعبة في RAWG: {e}")
            return None
    
    async def _get_game_screenshots(self, game_id: int, max_images: int) -> List[LicensedImage]:
        """الحصول على لقطات الشاشة للعبة"""
        try:
            params = {'page_size': max_images}
            if self.api_key:
                params['key'] = self.api_key
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/games/{game_id}/screenshots",
                    params=params
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return self._process_rawg_screenshots(data.get('results', []))
                    else:
                        logger.warning(f"⚠️ فشل في الحصول على لقطات الشاشة: {response.status}")
                        
            return []
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على لقطات الشاشة: {e}")
            return []
    
    def _process_rawg_screenshots(self, screenshots: List[Dict]) -> List[LicensedImage]:
        """معالجة لقطات الشاشة من RAWG"""
        licensed_images = []
        
        for screenshot in screenshots:
            try:
                licensed_image = LicensedImage(
                    url=screenshot['image'],
                    source='RAWG',
                    license_type='RAWG Terms of Use',
                    attribution='Screenshot provided by RAWG.io',
                    game_name='',
                    image_type='screenshot',
                    width=screenshot.get('width', 0),
                    height=screenshot.get('height', 0),
                    safe_for_adsense=True,
                    copyright_free=True
                )
                
                licensed_images.append(licensed_image)
                
            except Exception as e:
                logger.warning(f"⚠️ خطأ في معالجة لقطة شاشة RAWG: {e}")
                continue
        
        return licensed_images
    
    def _clean_game_name(self, game_name: str) -> str:
        """تنظيف اسم اللعبة للبحث"""
        # نفس منطق IGDB
        clean_name = re.sub(r'\b(game|لعبة|review|مراجعة|news|أخبار)\b', '', game_name, flags=re.IGNORECASE)
        clean_name = re.sub(r'[^\w\s]', ' ', clean_name)
        clean_name = re.sub(r'\s+', ' ', clean_name).strip()
        return clean_name

class SteamImageProvider:
    """موفر الصور من Steam Store API"""

    def __init__(self):
        self.base_url = "https://store.steampowered.com/api"

        # Cache للصور
        self.image_cache = {}
        self.cache_duration = timedelta(hours=24)

        logger.info("🚂 تم تهيئة موفر صور Steam")

    async def search_game_images(self, game_name: str, max_images: int = 3) -> List[LicensedImage]:
        """البحث عن صور اللعبة في Steam"""
        try:
            clean_name = self._clean_game_name(game_name)
            cache_key = f"steam_{hashlib.md5(clean_name.encode()).hexdigest()}"

            # فحص الكاش
            if cache_key in self.image_cache:
                cached_data = self.image_cache[cache_key]
                if datetime.now() - cached_data['timestamp'] < self.cache_duration:
                    logger.info(f"📦 استخدام صور Steam محفوظة لـ {game_name}")
                    return cached_data['images']

            # البحث عن اللعبة
            app_id = await self._search_steam_app(clean_name)
            if not app_id:
                return []

            # الحصول على تفاصيل اللعبة
            app_details = await self._get_app_details(app_id)
            if not app_details:
                return []

            # استخراج الصور
            images = self._extract_steam_images(app_details, max_images)

            # حفظ في الكاش
            self.image_cache[cache_key] = {
                'images': images,
                'timestamp': datetime.now()
            }

            logger.info(f"🚂 تم العثور على {len(images)} صورة لـ {game_name} من Steam")
            return images

        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن صور {game_name} في Steam: {e}")
            return []

    async def _search_steam_app(self, game_name: str) -> Optional[int]:
        """البحث عن معرف التطبيق في Steam"""
        try:
            # استخدام Steam Web API للبحث
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    "https://api.steampowered.com/ISteamApps/GetAppList/v2/"
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        apps = data.get('applist', {}).get('apps', [])

                        # البحث عن اللعبة بالاسم
                        for app in apps:
                            if self._match_game_name(game_name, app.get('name', '')):
                                return app['appid']

            return None

        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن التطبيق في Steam: {e}")
            return None

    async def _get_app_details(self, app_id: int) -> Optional[Dict]:
        """الحصول على تفاصيل التطبيق من Steam"""
        try:
            params = {
                'appids': app_id,
                'l': 'english'
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/appdetails",
                    params=params
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        app_data = data.get(str(app_id))
                        if app_data and app_data.get('success'):
                            return app_data.get('data')

            return None

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على تفاصيل التطبيق: {e}")
            return None

    def _extract_steam_images(self, app_details: Dict, max_images: int) -> List[LicensedImage]:
        """استخراج الصور من تفاصيل Steam"""
        licensed_images = []

        try:
            game_name = app_details.get('name', '')

            # صورة الرأس
            if app_details.get('header_image'):
                header_image = LicensedImage(
                    url=app_details['header_image'],
                    source='Steam',
                    license_type='Steam Store Promotional Use',
                    attribution=f'Image from Steam Store page for {game_name}',
                    game_name=game_name,
                    image_type='header',
                    safe_for_adsense=True,
                    copyright_free=False  # Steam images require attribution
                )
                licensed_images.append(header_image)

            # لقطات الشاشة
            screenshots = app_details.get('screenshots', [])
            for i, screenshot in enumerate(screenshots[:max_images-1]):
                if len(licensed_images) >= max_images:
                    break

                screenshot_image = LicensedImage(
                    url=screenshot.get('path_full', ''),
                    source='Steam',
                    license_type='Steam Store Promotional Use',
                    attribution=f'Screenshot from Steam Store page for {game_name}',
                    game_name=game_name,
                    image_type='screenshot',
                    safe_for_adsense=True,
                    copyright_free=False
                )
                licensed_images.append(screenshot_image)

        except Exception as e:
            logger.warning(f"⚠️ خطأ في استخراج صور Steam: {e}")

        return licensed_images

    def _match_game_name(self, search_name: str, steam_name: str) -> bool:
        """مطابقة اسم اللعبة"""
        search_words = set(search_name.lower().split())
        steam_words = set(steam_name.lower().split())

        # نسبة التطابق
        common_words = search_words & steam_words
        if len(common_words) >= min(2, len(search_words)):
            return True

        # فحص إذا كان الاسم يحتوي على الكلمات الأساسية
        return any(word in steam_name.lower() for word in search_words if len(word) > 3)

    def _clean_game_name(self, game_name: str) -> str:
        """تنظيف اسم اللعبة للبحث"""
        clean_name = re.sub(r'\b(game|لعبة|review|مراجعة|news|أخبار)\b', '', game_name, flags=re.IGNORECASE)
        clean_name = re.sub(r'[^\w\s]', ' ', clean_name)
        clean_name = re.sub(r'\s+', ' ', clean_name).strip()
        return clean_name

class PressKitImageProvider:
    """موفر الصور من Press Kits الرسمية للمطورين"""

    def __init__(self):
        # قائمة بمطوري الألعاب المعروفين وروابط Press Kits الخاصة بهم
        self.press_kit_sources = {
            'riot_games': {
                'base_url': 'https://www.riotgames.com/en/press',
                'games': ['league of legends', 'valorant', 'teamfight tactics', 'legends of runeterra'],
                'image_patterns': [r'https://.*\.riotgames\.com/.*\.(jpg|jpeg|png)']
            },
            'ubisoft': {
                'base_url': 'https://www.ubisoft.com/en-us/company/press',
                'games': ['assassins creed', 'far cry', 'watch dogs', 'rainbow six'],
                'image_patterns': [r'https://.*\.ubisoft\.com/.*\.(jpg|jpeg|png)']
            },
            'cd_projekt_red': {
                'base_url': 'https://www.cdprojektred.com/en/media',
                'games': ['cyberpunk 2077', 'the witcher', 'gwent'],
                'image_patterns': [r'https://.*\.cdprojektred\.com/.*\.(jpg|jpeg|png)']
            },
            'blizzard': {
                'base_url': 'https://blizzard.gamespress.com',
                'games': ['world of warcraft', 'overwatch', 'diablo', 'starcraft', 'hearthstone'],
                'image_patterns': [r'https://.*\.blizzard\.com/.*\.(jpg|jpeg|png)']
            },
            'epic_games': {
                'base_url': 'https://www.epicgames.com/site/en-US/news',
                'games': ['fortnite', 'rocket league', 'fall guys'],
                'image_patterns': [r'https://.*\.epicgames\.com/.*\.(jpg|jpeg|png)']
            }
        }

        # Cache للصور
        self.image_cache = {}
        self.cache_duration = timedelta(hours=48)  # مدة أطول للـ Press Kits

        logger.info("📰 تم تهيئة موفر صور Press Kits")

    async def search_game_images(self, game_name: str, max_images: int = 3) -> List[LicensedImage]:
        """البحث عن صور اللعبة في Press Kits"""
        try:
            clean_name = self._clean_game_name(game_name).lower()
            cache_key = f"presskit_{hashlib.md5(clean_name.encode()).hexdigest()}"

            # فحص الكاش
            if cache_key in self.image_cache:
                cached_data = self.image_cache[cache_key]
                if datetime.now() - cached_data['timestamp'] < self.cache_duration:
                    logger.info(f"📦 استخدام صور Press Kit محفوظة لـ {game_name}")
                    return cached_data['images']

            # البحث في Press Kits
            all_images = []

            for developer, info in self.press_kit_sources.items():
                # فحص إذا كانت اللعبة تنتمي لهذا المطور
                if any(game in clean_name for game in info['games']):
                    try:
                        logger.info(f"🔍 البحث في Press Kit لـ {developer}")

                        # محاولة الحصول على صور من Press Kit
                        images = await self._get_press_kit_images(developer, info, game_name, max_images)
                        all_images.extend(images)

                        if len(all_images) >= max_images:
                            break

                    except Exception as e:
                        logger.warning(f"⚠️ فشل في الحصول على صور من {developer}: {e}")
                        continue

            # حفظ في الكاش
            if all_images:
                self.image_cache[cache_key] = {
                    'images': all_images[:max_images],
                    'timestamp': datetime.now()
                }

            logger.info(f"📰 تم العثور على {len(all_images[:max_images])} صورة من Press Kits لـ {game_name}")
            return all_images[:max_images]

        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن صور Press Kit لـ {game_name}: {e}")
            return []

    async def _get_press_kit_images(self, developer: str, info: Dict, game_name: str, max_images: int) -> List[LicensedImage]:
        """الحصول على صور من Press Kit محدد"""
        try:
            # هذه دالة مبسطة - في التطبيق الحقيقي، ستحتاج لتحليل صفحات Press Kit
            # أو استخدام APIs خاصة بكل مطور

            # صور احتياطية عالية الجودة من مصادر معروفة
            fallback_images = await self._get_fallback_press_kit_images(developer, game_name, max_images)

            return fallback_images

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على صور Press Kit من {developer}: {e}")
            return []

    async def _get_fallback_press_kit_images(self, developer: str, game_name: str, max_images: int) -> List[LicensedImage]:
        """الحصول على صور احتياطية من مصادر Press Kit معروفة"""
        fallback_images = []

        # صور عالية الجودة من مصادر رسمية (أمثلة)
        if 'riot' in developer and any(game in game_name.lower() for game in ['league', 'valorant']):
            sample_images = [
                {
                    'url': 'https://images.contentstack.io/v3/assets/blt187521ff0727be24/blt7f0c87f09e2491c4/60ee0f8f4d3a8b0e8b6e8c8e/Valorant_KeyArt_2560x1440.jpg',
                    'description': 'Official Valorant Key Art',
                    'image_type': 'key_art'
                }
            ]
        elif 'ubisoft' in developer:
            sample_images = [
                {
                    'url': 'https://staticctf.akamaized.net/J3yJr34U2pZ2Ieem48Dwy9uqj5PNUQTn/**********/sample_ubisoft_image.jpg',
                    'description': 'Official Ubisoft Game Art',
                    'image_type': 'promotional'
                }
            ]
        else:
            return []

        for img_data in sample_images[:max_images]:
            licensed_image = LicensedImage(
                url=img_data['url'],
                source=f'{developer.replace("_", " ").title()} Press Kit',
                license_type='Official Press Kit License',
                attribution=f'Official image from {developer.replace("_", " ").title()} Press Kit',
                game_name=game_name,
                image_type=img_data['image_type'],
                safe_for_adsense=True,
                copyright_free=True  # Press Kits عادة مخصصة للاستخدام الإعلامي
            )
            fallback_images.append(licensed_image)

        return fallback_images

    def _clean_game_name(self, game_name: str) -> str:
        """تنظيف اسم اللعبة للبحث"""
        clean_name = re.sub(r'\b(game|لعبة|review|مراجعة|news|أخبار)\b', '', game_name, flags=re.IGNORECASE)
        clean_name = re.sub(r'[^\w\s]', ' ', clean_name)
        clean_name = re.sub(r'\s+', ' ', clean_name).strip()
        return clean_name

class LicensedImageManager:
    """مدير الصور المرخصة الرئيسي"""

    def __init__(self):
        self.providers = {
            'press_kits': PressKitImageProvider(),
            'igdb': IGDBImageProvider(),
            'rawg': RAWGImageProvider(),
            'steam': SteamImageProvider()
        }

        # إعدادات الأولوية (Press Kits أولاً لأنها الأكثر أماناً قانونياً)
        self.provider_priority = ['press_kits', 'igdb', 'rawg', 'steam']

        # إحصائيات الاستخدام
        self.usage_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'provider_usage': {provider: 0 for provider in self.providers},
            'cache_hits': 0
        }

        logger.info("🎨 تم تهيئة مدير الصور المرخصة")

    async def get_licensed_images_for_game(self, game_name: str, max_images: int = 3) -> List[LicensedImage]:
        """الحصول على صور مرخصة للعبة - يجرب كل API بالتدرج"""
        self.usage_stats['total_requests'] += 1

        try:
            all_images = []

            # جرب كل موفر حسب الأولوية مع معالجة الأخطاء
            for provider_name in self.provider_priority:
                if len(all_images) >= max_images:
                    break

                provider = self.providers[provider_name]
                try:
                    logger.info(f"🔍 البحث في {provider_name.upper()} عن صور {game_name}")

                    # محاولة البحث مع timeout
                    images = await asyncio.wait_for(
                        provider.search_game_images(game_name, max_images - len(all_images)),
                        timeout=30.0  # 30 ثانية timeout
                    )

                    if images:
                        # تحديث اسم اللعبة في الصور
                        for img in images:
                            img.game_name = game_name

                        all_images.extend(images)
                        self.usage_stats['provider_usage'][provider_name] += len(images)

                        logger.info(f"✅ تم العثور على {len(images)} صورة من {provider_name.upper()}")

                        # إذا حصلنا على صور كافية، توقف
                        if len(all_images) >= max_images:
                            break
                    else:
                        logger.info(f"⚠️ لم يتم العثور على صور في {provider_name.upper()}")

                    # تأخير قصير بين الطلبات
                    await asyncio.sleep(0.5)

                except asyncio.TimeoutError:
                    logger.warning(f"⏰ انتهت مهلة البحث في {provider_name.upper()}")
                    continue
                except Exception as e:
                    logger.warning(f"⚠️ فشل في الحصول على صور من {provider_name.upper()}: {e}")
                    # لا نتوقف، نجرب الموفر التالي
                    continue

            if all_images:
                self.usage_stats['successful_requests'] += 1
                logger.info(f"🎯 إجمالي الصور المرخصة الموجودة: {len(all_images)} لـ {game_name}")

                # ترتيب الصور حسب الأولوية (Press Kits أولاً، ثم IGDB، إلخ)
                priority_order = {'Press Kit': 1, 'IGDB': 2, 'RAWG': 3, 'Steam': 4}
                all_images.sort(key=lambda img: priority_order.get(img.source, 5))

            else:
                logger.warning(f"❌ لم يتم العثور على أي صور مرخصة لـ {game_name} من جميع المصادر")

            return all_images[:max_images]

        except Exception as e:
            logger.error(f"❌ خطأ عام في الحصول على صور مرخصة لـ {game_name}: {e}")
            return []

    def get_usage_stats(self) -> Dict:
        """الحصول على إحصائيات الاستخدام"""
        return {
            **self.usage_stats,
            'success_rate': (
                self.usage_stats['successful_requests'] /
                max(self.usage_stats['total_requests'], 1)
            ) * 100
        }

    async def test_all_providers(self) -> Dict:
        """اختبار جميع موفري الصور"""
        test_results = {}
        test_game = "The Witcher 3"

        for provider_name, provider in self.providers.items():
            try:
                logger.info(f"🧪 اختبار {provider_name.upper()}...")

                images = await provider.search_game_images(test_game, 1)
                test_results[provider_name] = {
                    'available': len(images) > 0,
                    'images_found': len(images),
                    'error': None
                }

                if images:
                    logger.info(f"✅ {provider_name.upper()} يعمل بشكل صحيح")
                else:
                    logger.warning(f"⚠️ {provider_name.upper()} لم يعيد صور")

            except Exception as e:
                test_results[provider_name] = {
                    'available': False,
                    'images_found': 0,
                    'error': str(e)
                }
                logger.error(f"❌ فشل اختبار {provider_name.upper()}: {e}")

        return test_results

# إنشاء مثيل عام
licensed_image_manager = LicensedImageManager()
