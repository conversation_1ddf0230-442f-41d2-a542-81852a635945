# نظام البحث العميق باستخدام Gemini 2.5 Pro
import asyncio
import time
import json
import aiohttp
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from .logger import logger
from config.settings import BotConfig, google_api_manager

class SearchDepth(Enum):
    """مستويات عمق البحث"""
    BASIC = "basic"
    STANDARD = "standard"
    ADVANCED = "advanced"
    COMPREHENSIVE = "comprehensive"

@dataclass
class GeminiSearchResult:
    """نتيجة البحث من Gemini"""
    title: str
    content: str
    url: str
    source: str
    relevance_score: float
    has_web_search: bool
    search_depth: str
    timestamp: datetime
    metadata: Dict[str, Any]

class GeminiDeepSearch:
    """نظام البحث العميق باستخدام Gemini 2.5 Pro مع تقنية البحث المدمجة"""
    
    def __init__(self):
        self.enabled = bool(google_api_manager and google_api_manager.get_key())
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
        self.model_name = "gemini-2.5-pro"  # استخدام Gemini 2.5 Pro فقط
        
        # إحصائيات الاستخدام
        self.usage_stats = {
            'total_searches': 0,
            'successful_searches': 0,
            'failed_searches': 0,
            'web_search_detected': 0,
            'average_response_time': 0,
            'daily_usage': 0,
            'last_reset': datetime.now().date()
        }
        
        # إعدادات البحث
        self.search_config = {
            'timeout': 60,
            'max_retries': 3,
            'retry_delay': 5,
            'max_tokens': 8192,
            'temperature': 0.7,
            'top_p': 0.9,
            'top_k': 40
        }
        
        # قوالب البحث المحسنة
        self.search_templates = {
            SearchDepth.BASIC: """
            قدم معلومات شاملة ومحدثة حول: {query}

            يرجى تقديم:
            1. معلومات أساسية حول الموضوع
            2. آخر التطورات والأخبار الحديثة
            3. مصادر موثوقة ومراجع

            استخدم أحدث المعلومات المتاحة لديك وقدم تحليلاً مفيداً.
            """,
            
            SearchDepth.STANDARD: """
            قدم تحليلاً مفصلاً وشاملاً حول: {query}

            يرجى تقديم:
            1. نظرة عامة شاملة ومحدثة
            2. آخر الأخبار والتطورات الحديثة
            3. تحليل للاتجاهات الحالية
            4. مصادر موثوقة ومراجع
            5. معلومات تقنية مفصلة

            استخدم أحدث المعلومات المتاحة لديك وقدم تحليلاً عميقاً ومفيداً.
            """,
            
            SearchDepth.ADVANCED: """
            قدم تحليلاً عميقاً ومتقدماً حول: {query}

            يرجى تقديم:
            1. تحليل شامل ومعمق للموضوع
            2. آخر الأخبار والتطورات من مصادر متعددة
            3. تحليل الاتجاهات والتوقعات المستقبلية
            4. مقارنات مع مواضيع ذات صلة
            5. رؤى تحليلية عميقة
            6. مصادر متنوعة وموثوقة
            7. معلومات تقنية مفصلة ودقيقة

            استخدم أحدث المعلومات المتاحة لديك وقدم أشمل تحليل ممكن.
            """,
            
            SearchDepth.COMPREHENSIVE: """
            قدم تقريراً شاملاً ومتكاملاً حول: {query}

            يرجى تقديم تقريراً شاملاً يتضمن:

            📊 التحليل الأساسي:
            1. نظرة عامة شاملة ومفصلة
            2. السياق التاريخي والخلفية
            3. الوضع الحالي والتطورات الأخيرة

            🔍 التحليل المتقدم:
            4. آخر الأخبار من مصادر متعددة
            5. تحليل الاتجاهات والإحصائيات
            6. مقارنات مع مواضيع مشابهة
            7. رؤى من خبراء المجال

            🎯 التحليل العميق:
            8. تحليل نقدي للمعلومات
            9. التوقعات والاتجاهات المستقبلية
            10. التأثيرات والنتائج المحتملة

            📚 المصادر والمراجع:
            11. مصادر موثوقة ومتنوعة
            12. مراجع للمزيد من القراءة

            استخدم أحدث المعلومات المتاحة لديك وقدم أشمل تحليل ممكن.
            """
        }
        
        logger.info(f"🔍 تم تهيئة نظام البحث العميق Gemini 2.5 Pro - الحالة: {'مفعل' if self.enabled else 'معطل'}")
    
    async def deep_search(self, 
                         query: str, 
                         search_depth: SearchDepth = SearchDepth.ADVANCED,
                         max_results: int = 10,
                         include_sources: bool = True) -> List[GeminiSearchResult]:
        """البحث العميق باستخدام Gemini 2.5 Pro"""
        
        if not self.enabled:
            logger.warning("⚠️ نظام البحث العميق Gemini غير مفعل")
            return []
        
        start_time = time.time()
        self.usage_stats['total_searches'] += 1
        self.usage_stats['daily_usage'] += 1
        
        logger.info(f"🔍 بدء البحث العميق Gemini 2.5 Pro: {query}")
        logger.info(f"📊 العمق: {search_depth.value}, النتائج المطلوبة: {max_results}")
        
        try:
            # إعداد الطلب
            search_prompt = self.search_templates[search_depth].format(query=query)
            
            # تنفيذ البحث
            response = await self._execute_search_request(search_prompt)
            
            if response:
                # معالجة النتائج
                results = await self._process_search_response(
                    response, query, search_depth, max_results, include_sources
                )
                
                # تحديث الإحصائيات
                execution_time = time.time() - start_time
                self.usage_stats['successful_searches'] += 1
                self.usage_stats['average_response_time'] = (
                    (self.usage_stats['average_response_time'] * (self.usage_stats['successful_searches'] - 1) + execution_time) 
                    / self.usage_stats['successful_searches']
                )
                
                # فحص وجود بحث ويب
                if self._detect_web_search_usage(response):
                    self.usage_stats['web_search_detected'] += 1
                
                logger.info(f"✅ البحث العميق نجح في {execution_time:.2f} ثانية - {len(results)} نتيجة")
                return results
            
            else:
                self.usage_stats['failed_searches'] += 1
                logger.error("❌ فشل في الحصول على استجابة من Gemini")
                return []
                
        except Exception as e:
            self.usage_stats['failed_searches'] += 1
            logger.error(f"❌ خطأ في البحث العميق Gemini: {e}")
            return []
    
    async def _execute_search_request(self, prompt: str) -> Optional[Dict]:
        """تنفيذ طلب البحث إلى Gemini 2.5 Pro"""
        
        url = f"{self.base_url}/models/{self.model_name}:generateContent"
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        params = {
            'key': google_api_manager.get_key()
        }
        
        payload = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": self.search_config['temperature'],
                "topK": self.search_config['top_k'],
                "topP": self.search_config['top_p'],
                "maxOutputTokens": self.search_config['max_tokens'],
            },
            "safetySettings": [
                {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
            ]
        }
        
        for attempt in range(self.search_config['max_retries']):
            try:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.search_config['timeout'])) as session:
                    async with session.post(url, headers=headers, params=params, json=payload) as response:
                        if response.status == 200:
                            result = await response.json()
                            return result
                        elif response.status == 403:
                            # تبديل المفتاح في حالة انتهاء الحصة
                            logger.warning("⚠️ انتهت حصة المفتاح الحالي، جاري التبديل...")
                            google_api_manager.rotate_key()
                            params['key'] = google_api_manager.get_key()
                        else:
                            logger.warning(f"⚠️ استجابة غير متوقعة: {response.status}")
                            
            except asyncio.TimeoutError:
                logger.warning(f"⏰ انتهت مهلة المحاولة {attempt + 1}")
            except Exception as e:
                logger.warning(f"⚠️ خطأ في المحاولة {attempt + 1}: {e}")
            
            if attempt < self.search_config['max_retries'] - 1:
                await asyncio.sleep(self.search_config['retry_delay'])
        
        return None

    async def _process_search_response(self,
                                     response: Dict,
                                     query: str,
                                     search_depth: SearchDepth,
                                     max_results: int,
                                     include_sources: bool) -> List[GeminiSearchResult]:
        """معالجة استجابة البحث وتحويلها إلى نتائج منظمة"""

        try:
            # استخراج النص من الاستجابة
            candidates = response.get('candidates', [])
            if not candidates:
                return []

            content = candidates[0].get('content', {})
            parts = content.get('parts', [])
            if not parts or len(parts) == 0:
                logger.warning("⚠️ لا توجد أجزاء في استجابة Gemini")
                return []

            if 'text' not in parts[0]:
                logger.warning("⚠️ لا يوجد نص في الجزء الأول من استجابة Gemini")
                return []

            full_text = parts[0].get('text', '')
            if not full_text:
                logger.warning("⚠️ النص فارغ في استجابة Gemini")
                return []

            # تقسيم النص إلى أقسام منطقية
            sections = self._split_content_into_sections(full_text)

            results = []
            for i, section in enumerate(sections[:max_results]):
                if len(section.strip()) > 100:  # تجاهل الأقسام القصيرة جداً

                    # استخراج العنوان من بداية القسم
                    title = self._extract_title_from_section(section, query, i)

                    # تنظيف المحتوى
                    clean_content = self._clean_content(section)

                    # حساب نقاط الصلة
                    relevance_score = self._calculate_relevance_score(clean_content, query)

                    # إنشاء نتيجة البحث
                    result = GeminiSearchResult(
                        title=title,
                        content=clean_content,
                        url=f"gemini://deep-search/{query.replace(' ', '-')}-{i+1}",
                        source="Gemini 2.5 Pro Deep Search",
                        relevance_score=relevance_score,
                        has_web_search=self._detect_web_search_usage(response),
                        search_depth=search_depth.value,
                        timestamp=datetime.now(),
                        metadata={
                            'model': self.model_name,
                            'search_query': query,
                            'section_index': i,
                            'total_sections': len(sections),
                            'content_length': len(clean_content),
                            'processing_time': time.time()
                        }
                    )

                    results.append(result)

            return results

        except Exception as e:
            logger.error(f"❌ خطأ في معالجة استجابة البحث: {e}")
            return []

    def _split_content_into_sections(self, text: str) -> List[str]:
        """تقسيم النص إلى أقسام منطقية"""

        # تقسيم بناءً على العناوين والفقرات
        sections = []

        # تقسيم بناءً على الأرقام والنقاط
        numbered_sections = text.split('\n\n')

        for section in numbered_sections:
            section = section.strip()
            if section:
                # تقسيم الأقسام الطويلة جداً
                if len(section) > 1000:
                    # تقسيم بناءً على الجمل
                    sentences = section.split('. ')
                    current_section = ""

                    for sentence in sentences:
                        if len(current_section + sentence) < 800:
                            current_section += sentence + ". "
                        else:
                            if current_section:
                                sections.append(current_section.strip())
                            current_section = sentence + ". "

                    if current_section:
                        sections.append(current_section.strip())
                else:
                    sections.append(section)

        return sections

    def _extract_title_from_section(self, section: str, query: str, index: int) -> str:
        """استخراج عنوان مناسب من القسم"""

        lines = section.split('\n')
        first_line = lines[0].strip()

        # إذا كان السطر الأول يبدو كعنوان
        if len(first_line) < 100 and any(char in first_line for char in [':', '؟', '!', '.']):
            # تنظيف العنوان من الأرقام والرموز
            title = first_line.strip('0123456789.- ')
            if len(title) > 10:
                return title

        # استخراج الكلمات المفتاحية من بداية القسم
        words = section.split()[:10]
        title_words = []

        for word in words:
            if len(' '.join(title_words + [word])) < 80:
                title_words.append(word)
            else:
                break

        if title_words:
            return ' '.join(title_words) + "..."

        # عنوان افتراضي
        return f"نتيجة البحث {index + 1}: {query}"

    def _clean_content(self, content: str) -> str:
        """تنظيف وتنسيق المحتوى"""

        # إزالة الأسطر الفارغة الزائدة
        lines = [line.strip() for line in content.split('\n') if line.strip()]

        # إعادة تجميع النص
        cleaned = '\n'.join(lines)

        # إزالة الرموز الزائدة
        cleaned = cleaned.replace('**', '').replace('##', '').replace('###', '')

        # تحسين التنسيق
        cleaned = cleaned.replace('\n\n\n', '\n\n')

        return cleaned.strip()

    def _calculate_relevance_score(self, content: str, query: str) -> float:
        """حساب نقاط الصلة بين المحتوى والاستعلام"""

        query_words = query.lower().split()
        content_lower = content.lower()

        # حساب تكرار الكلمات المفتاحية
        keyword_matches = sum(1 for word in query_words if word in content_lower)
        keyword_score = min(keyword_matches / len(query_words), 1.0) * 0.4

        # حساب طول المحتوى (المحتوى الأطول = نقاط أعلى)
        length_score = min(len(content) / 1000, 1.0) * 0.2

        # حساب جودة المحتوى (وجود معلومات مفيدة)
        quality_indicators = ['تحديث', 'جديد', 'أخبار', 'معلومات', 'تطوير', 'إصدار']
        quality_matches = sum(1 for indicator in quality_indicators if indicator in content_lower)
        quality_score = min(quality_matches / len(quality_indicators), 1.0) * 0.3

        # نقاط إضافية للمحتوى المنظم
        structure_score = 0.1 if any(char in content for char in [':', '•', '-', '1.', '2.']) else 0

        total_score = keyword_score + length_score + quality_score + structure_score
        return min(total_score, 1.0)

    def _detect_web_search_usage(self, response: Dict) -> bool:
        """فحص ما إذا كان Gemini استخدم البحث على الويب"""

        # فحص وجود مؤشرات البحث على الويب في الاستجابة
        candidates = response.get('candidates', [])
        if not candidates:
            return False

        content = candidates[0].get('content', {})
        parts = content.get('parts', [])
        if not parts or len(parts) == 0:
            return False

        if 'text' not in parts[0]:
            return False

        text = parts[0].get('text', '').lower()

        # مؤشرات البحث على الويب
        web_indicators = [
            'وفقاً لأحدث المعلومات',
            'حسب المصادر الحديثة',
            'بناءً على البحث',
            'المصادر تشير',
            'according to recent',
            'based on current',
            'latest information',
            'recent sources',
            'web search',
            'online sources'
        ]

        return any(indicator in text for indicator in web_indicators)

    def get_usage_stats(self) -> Dict:
        """الحصول على إحصائيات الاستخدام"""

        total_searches = self.usage_stats['total_searches']
        success_rate = (self.usage_stats['successful_searches'] / total_searches * 100) if total_searches > 0 else 0
        web_search_rate = (self.usage_stats['web_search_detected'] / self.usage_stats['successful_searches'] * 100) if self.usage_stats['successful_searches'] > 0 else 0

        return {
            'enabled': self.enabled,
            'model': self.model_name,
            'total_searches': total_searches,
            'successful_searches': self.usage_stats['successful_searches'],
            'failed_searches': self.usage_stats['failed_searches'],
            'success_rate': round(success_rate, 2),
            'web_search_detected': self.usage_stats['web_search_detected'],
            'web_search_rate': round(web_search_rate, 2),
            'average_response_time': round(self.usage_stats['average_response_time'], 2),
            'daily_usage': self.usage_stats['daily_usage'],
            'last_reset': self.usage_stats['last_reset']
        }

    async def test_search_capability(self, test_query: str = "latest gaming news today") -> Dict:
        """اختبار قدرة البحث"""

        logger.info(f"🧪 اختبار قدرة البحث العميق Gemini 2.5 Pro...")

        start_time = time.time()

        try:
            results = await self.deep_search(
                query=test_query,
                search_depth=SearchDepth.STANDARD,
                max_results=3
            )

            execution_time = time.time() - start_time

            test_result = {
                'success': len(results) > 0,
                'execution_time': round(execution_time, 2),
                'results_count': len(results),
                'web_search_detected': any(result.has_web_search for result in results),
                'average_relevance': round(sum(result.relevance_score for result in results) / len(results), 2) if results else 0,
                'test_query': test_query,
                'timestamp': datetime.now().isoformat()
            }

            if results:
                test_result['sample_result'] = {
                    'title': results[0].title,
                    'content_preview': results[0].content[:200] + "..." if len(results[0].content) > 200 else results[0].content,
                    'relevance_score': results[0].relevance_score,
                    'has_web_search': results[0].has_web_search
                }

            logger.info(f"✅ اختبار البحث {'نجح' if test_result['success'] else 'فشل'} في {execution_time:.2f} ثانية")
            return test_result

        except Exception as e:
            logger.error(f"❌ خطأ في اختبار البحث: {e}")
            return {
                'success': False,
                'error': str(e),
                'execution_time': time.time() - start_time,
                'test_query': test_query,
                'timestamp': datetime.now().isoformat()
            }

# إنشاء مثيل عام
gemini_deep_search = GeminiDeepSearch()
